/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useState, useMemo, useEffect } from "react";
import "../../../styling/saved.css";
import {
    Table,
    TableBody,
    TableCell,
    TableHeader,
    TableRow,
} from "components/shadcn/ui/table";
import { But<PERSON> } from "components/shadcn/ui/button";
import SvgChevronSelectorIcon from "components/common/iconComponents/chevronSelectorIcon";
import SvgBackArrowIcon from "components/common/iconComponents/backArrowIcon";
import Loader from "components/common/loader";
import { formatPercentage, getFinancialStr } from "helpers";
import { showCampaignFeature } from "components/utils/network/endpoints";
import { Checkbox } from "components/shadcn/ui/checkbox";
import {
    Trash2,
    ChevronLeft,
    ChevronRight,
    Settings,
    LockIcon,
    Eye,
} from "lucide-react";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "components/shadcn/ui/select";
import { Alert } from "components/shadcn/ui/alert";
import SvgDownloadIcon from "components/common/iconComponents/downloadIcon";
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from "components/shadcn/ui/tooltip";
import { Card, CardContent } from "components/shadcn/ui/card";
import { useCollectionDetails } from "./useCollectionDetails";
import DeleteCompaniesModal from "../delete-companies/deleteCompaniesModal";
import { useNavigate } from "react-router-dom";

interface RowData {
    id: string;
    company: string;
    industry: string;
    revenue: string;
    netProfit: string;
    netProfitPercentage: string;
    debtRatio: string;
    turnover: string;
    turnoverStatus: string;
    netProfitEstimate: string;
    companyNumber: string;
}

const CollectionDetails: React.FC = () => {
    const navigate = useNavigate();
    const {
        collectionCopy,
        collection,
        showDeleteSuccess,
        deleteCompaniesError,
        combinedData,
        isLoading,
        createExcelExport,
        table,
        pagination,
        setPagination,
        pageSizeOptions,
        handleCampaignNavigation,
        handleBackNavigation,
        isEditMode,
        setIsEditMode,
        selectedCompanyIds,
        handleSelectCompany,
        handleSelectAllCompanies,
        handleCancelEdit,
        handleDeleteCompanies,
        showDeleteModal,
        setShowDeleteModal,
        isAllSelected,
    } = useCollectionDetails();

    return (
        <div className="fullScreen">
            {isLoading && <Loader />}
            <div className="container max-w-[80%]">
                <DeleteCompaniesModal
                    showModal={showDeleteModal}
                    onDelete={handleDeleteCompanies}
                    onCancel={() => setShowDeleteModal(false)}
                    selectedCount={selectedCompanyIds.length}
                />
                <div className="savedFiltersScreen">
                    <div
                        className="text-sm text-gray-600 gap-2 cursor-pointer flex flex-row item-center"
                        onClick={handleBackNavigation}
                    >
                        {" "}
                        <SvgBackArrowIcon /> Back
                    </div>
                    <div className="sfTitles flex flex-row items-center justify-between">
                        <div className="flex flex-col gap-5">
                            <div className="display-sm semibold text-gray-900 text-left flex flex-row gap-4 items-center">
                                {collection?.name}{" "}
                                {collection.isLockedForEmail || collection.isLockedForLetter ? (
                                    <TooltipProvider>
                                        <Tooltip>
                                            <TooltipTrigger asChild>
                                                <LockIcon
                                                    height={20}
                                                    width={20}
                                                    className="text-gray-700"
                                                />
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                {collection.isLockedForLetter &&
                                                    collection.isLockedForEmail
                                                    ? "This collection is locked for email and letter campaign"
                                                    : collection.isLockedForEmail
                                                        ? "This collection is locked for email campaign"
                                                        : "This collection is locked for letter campaign"}
                                            </TooltipContent>
                                        </Tooltip>
                                    </TooltipProvider>
                                ) : null}
                            </div>
                            <div className="flex flex-row items-center gap-2">
                                Total companies in the collection: {combinedData?.length}
                            </div>
                        </div>
                        <div className="flex flex-row gap-3">
                            {isEditMode ? (
                                <>
                                    <Button variant="outline" onClick={handleCancelEdit}>
                                        <div className="font-inter text-sm font-semibold leading-5 text-left underline-from-font">
                                            Cancel
                                        </div>
                                    </Button>
                                    <Button
                                        variant="destructive"
                                        onClick={() => setShowDeleteModal(true)}
                                        disabled={selectedCompanyIds.length === 0}
                                    >
                                        <div className="text-white font-inter text-sm font-semibold leading-5 text-left underline-from-font flex items-center gap-2">
                                            <Trash2 size={16} /> Delete {selectedCompanyIds.length}{" "}
                                            {selectedCompanyIds.length === 1
                                                ? "Company"
                                                : "Companies"}
                                        </div>
                                    </Button>
                                </>
                            ) : (
                                <>
                                    {collection?.isAddedToCampaign ? (
                                        <TooltipProvider>
                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <div>
                                                        <Button
                                                            variant="outline"
                                                            onClick={() => setIsEditMode(true)}
                                                            disabled
                                                        >
                                                            <div className="font-inter text-sm font-semibold leading-5 text-left underline-from-font flex items-center gap-2">
                                                                <Settings size={16} /> Manage Companies
                                                            </div>
                                                        </Button>
                                                    </div>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    You can't manage companies from a collection that is used in a campaign.
                                                </TooltipContent>
                                            </Tooltip>
                                        </TooltipProvider>
                                    ) : (
                                        <Button
                                            variant="outline"
                                            onClick={() => setIsEditMode(true)}
                                        >
                                            <div className="font-inter text-sm font-semibold leading-5 text-left underline-from-font flex items-center gap-2">
                                                <Settings size={16} /> Manage Companies
                                            </div>
                                        </Button>
                                    )}
                                    <Button variant="outline" onClick={() => createExcelExport()}>
                                        <div className="font-inter text-sm font-semibold leading-5 text-left underline-from-font flex flex-row items-center gap-2">
                                            <SvgDownloadIcon /> {collectionCopy.downloadCollection}
                                        </div>
                                    </Button>
                                    {showCampaignFeature && (
                                        <Button
                                            variant="primary"
                                            onClick={handleCampaignNavigation}
                                        >
                                            <div className="text-white font-inter text-sm font-semibold leading-5 text-left underline-from-font">
                                                {collectionCopy.newCampaign}
                                            </div>
                                        </Button>
                                    )}
                                </>
                            )}
                        </div>
                    </div>
                    {showDeleteSuccess && (
                        <Alert variant="success" title="Companies deleted successfully!" />
                    )}
                    {deleteCompaniesError.showError && (
                        <Alert
                            variant="destructive"
                            title="Failed to delete collection"
                            description={deleteCompaniesError.errorMessage as string}
                        />
                    )}
                    {/* {deleteCompaniesError.statusCode === 400 &&
                        <Card className="border-none">
                            <CardContent className="pb-0 border-none shadow-none">
                                <ul className="list-disc pl-6 text-left border-none">
                                    {collectionCopy.guidanceBannerSubtext.map((item, index) => (
                                        <li key={index} className="text-gray-700 text-sm mb-1">
                                            {item}
                                        </li>
                                    ))}
                                </ul>
                            </CardContent>
                        </Card>
                    } */}
                    <div className="border border-gray-200 rounded-[12px] max-h-[500px] overflow-y-auto w-full">
                        <Table
                            key={combinedData.length}
                            className="border-separate border-spacing-0 w-full"
                        >
                            <TableHeader>
                                {table.getHeaderGroups().map((headerGroup) => (
                                    <TableRow key={headerGroup.id}>
                                        {isEditMode && (
                                            <TableCell className="cursor-pointer border-b border-gray-200">
                                                {/* <Checkbox
                                                    checked={isAllSelected}
                                                    onCheckedChange={handleSelectAllCompanies}
                                                /> */}
                                            </TableCell>
                                        )}
                                        {headerGroup.headers.map((header) => (
                                            <TableCell
                                                key={header.id}
                                                className="cursor-pointer border-b border-gray-200"
                                                onClick={header.column.getToggleSortingHandler()}
                                            >
                                                {header.isPlaceholder ? null : (
                                                    <div className="flex flex-row gap-2 items-center text-sm font-medium text-gray-600">
                                                        {header.column.columnDef.header as string}
                                                        {header.column.getIsSorted() ? (
                                                            header.column.getIsSorted() === "asc" ? (
                                                                <SvgChevronSelectorIcon />
                                                            ) : (
                                                                <SvgChevronSelectorIcon />
                                                            )
                                                        ) : (
                                                            <SvgChevronSelectorIcon />
                                                        )}
                                                    </div>
                                                )}
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                ))}
                            </TableHeader>
                            <TableBody>
                                {table.getRowModel().rows.map((row) => (
                                    <TableRow key={row.id}>
                                        {isEditMode && (
                                            <TableCell className="border-b border-gray-200">
                                                <Checkbox
                                                    checked={selectedCompanyIds.includes(
                                                        row.original.companyNumber
                                                    )}
                                                    onCheckedChange={(checked) =>
                                                        handleSelectCompany(
                                                            row.original.companyNumber,
                                                            !!checked
                                                        )
                                                    }
                                                />
                                            </TableCell>
                                        )}
                                        {row.getVisibleCells().map((cell) => {
                                            const isCompanyColumn = cell.column.id === "company";
                                            const isConvertRevenueColumn =
                                                cell.column.id === "revenue";
                                            const isConvertCurrencyColumn =
                                                cell.column.id === "netProfit";
                                            const isInsightsColumn = cell.column.id === "insights";
                                            const cellValue = cell.renderValue();

                                            // Handle insights column with eye icon
                                            if (isInsightsColumn) {
                                                const handleInsightsClick = () => {
                                                    navigate(`/company-insights/${row.original.companyNumber}`, {
                                                        state: {
                                                            companyName: row.original.company,
                                                            companyNumber: row.original.companyNumber
                                                        }
                                                    });
                                                };

                                                return (
                                                    <TableCell
                                                        key={cell.id}
                                                        className="border-b border-gray-200 font-inter text-sm text-center"
                                                    >
                                                        <button
                                                            onClick={handleInsightsClick}
                                                            className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200 inline-flex items-center justify-center"
                                                            title="View Company Insights"
                                                        >
                                                            <Eye className="h-4 w-4 text-gray-600 hover:text-blue-600" />
                                                        </button>
                                                    </TableCell>
                                                );
                                            }

                                            let displayValue = cellValue == null ? "-" : cellValue;

                                            if (cell.column.id === "netProfitPercentage") {
                                                displayValue =
                                                    displayValue != null && !isNaN(Number(displayValue))
                                                        ? formatPercentage(displayValue) + "%"
                                                        : "-";
                                            }
                                            if (cell.column.id === "debtRatio") {
                                                displayValue =
                                                    displayValue != null && !isNaN(Number(displayValue))
                                                        ? `${(Number(displayValue) / 100).toFixed(2)}`
                                                        : "-";
                                            }

                                            const renderValue = isConvertCurrencyColumn
                                                ? getFinancialStr(
                                                    displayValue,
                                                    cell.row.original.netProfitEstimate
                                                )
                                                : isConvertRevenueColumn
                                                    ? getFinancialStr(
                                                        cell.row.original.turnover,
                                                        cell.row.original.turnoverStatus
                                                    )
                                                    : displayValue;

                                            return (
                                                <TableCell
                                                    key={cell.id}
                                                    className={`border-b border-gray-200 font-inter text-sm text-left ${isCompanyColumn
                                                        ? "font-medium leading-5 text-gray-900"
                                                        : "font-normal leading-5 text-gray-600"
                                                        }`}
                                                >
                                                    {typeof renderValue === "string" ||
                                                        typeof renderValue === "number"
                                                        ? renderValue
                                                        : "-"}
                                                </TableCell>
                                            );
                                        })}
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </div>

                    <div className="flex items-center justify-between space-x-2 py-4">
                        <div className="flex items-center space-x-2">
                            <p className="text-sm font-medium">Rows per page</p>
                            <Select
                                value={`${pagination.pageSize}`}
                                onValueChange={(value) => {
                                    setPagination({
                                        pageIndex: 0,
                                        pageSize: Number(value),
                                    });
                                }}
                            >
                                <SelectTrigger className="h-8 w-[70px]">
                                    <SelectValue placeholder={pagination.pageSize} />
                                </SelectTrigger>
                                <SelectContent side="top" className="z-9999">
                                    {pageSizeOptions.map((pageSize) => (
                                        <SelectItem key={pageSize} value={`${pageSize}`}>
                                            {pageSize}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="flex items-center space-x-6 lg:space-x-8">
                            <div className="flex w-[100px] items-center justify-center text-sm font-medium">
                                Page {pagination.pageIndex + 1} of {table.getPageCount()}
                            </div>
                            <div className="flex items-center space-x-2">
                                <Button
                                    variant="outline"
                                    className="h-8 w-8 p-0"
                                    onClick={() => table.previousPage()}
                                    disabled={!table.getCanPreviousPage()}
                                >
                                    <span className="sr-only">Go to previous page</span>
                                    <ChevronLeft className="h-4 w-4" />
                                </Button>
                                <Button
                                    variant="outline"
                                    className="h-8 w-8 p-0"
                                    onClick={() => table.nextPage()}
                                    disabled={!table.getCanNextPage()}
                                >
                                    <span className="sr-only">Go to next page</span>
                                    <ChevronRight className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default CollectionDetails;
