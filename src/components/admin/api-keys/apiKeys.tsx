import React, { useState, useEffect, useRef, useCallback } from "react";
import { Button } from "components/shadcn/ui/button";
import { Alert } from "components/shadcn/ui/alert";
import { Plus } from "lucide-react";
import ApiKeysTable from "./apiKeysTable";
import CreateApiKeyDialog from "./createApiKeyDialog";
import ViewApiKeyDialog from "./viewApiKeyDialog";
import { ApiKey, CreateApiKeyPayload } from "../types";
import { getApiKeys, createApiKey, getApiKeyToken } from "../services";

const ApiKeys: React.FC = () => {
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [isLoadingToken, setIsLoadingToken] = useState(false);
  const [selectedApiKey, setSelectedApiKey] = useState<ApiKey | null>(null);
  const [selectedToken, setSelectedToken] = useState<string | null>(null);
  const [alert, setAlert] = useState<{
    type: "success" | "destructive";
    message: string;
  } | null>(null);
  const hasFetchedRef = useRef(false);

  // Auto-dismiss alert after 5 seconds
  useEffect(() => {
    if (alert) {
      const timer = setTimeout(() => {
        setAlert(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [alert]);

  const showAlert = useCallback((type: "success" | "destructive", message: string) => {
    setAlert({ type, message });
  }, []);

  const fetchApiKeys = useCallback(async () => {
    try {
      setIsLoading(true);
      const keys = await getApiKeys();
      setApiKeys(keys);
    } catch (error) {
      console.error("Error fetching API keys:", error);
      showAlert("destructive", "Failed to fetch API keys. Please try again.");
    } finally {
      setIsLoading(false);
    }
  }, [showAlert]);

  useEffect(() => {
    if (!hasFetchedRef.current) {
      hasFetchedRef.current = true;
      fetchApiKeys();
    }
  }, [fetchApiKeys]);

  const handleCreateApiKey = async (payload: CreateApiKeyPayload) => {
    try {
      setIsCreating(true);
      await createApiKey(payload);
      showAlert("success", "API key created successfully!");
      await fetchApiKeys(); // Refresh the list
    } catch (error) {
      console.error("Error creating API key:", error);
      showAlert("destructive", "Failed to create API key. Please try again.");
      throw error; // Re-throw to prevent dialog from closing
    } finally {
      setIsCreating(false);
    }
  };

  const handleRowClick = async (apiKey: ApiKey) => {
    try {
      setSelectedApiKey(apiKey);
      setSelectedToken(null);
      setIsViewDialogOpen(true);
      setIsLoadingToken(true);

      const tokenResponse = await getApiKeyToken(apiKey.id);
      setSelectedToken(tokenResponse.token);
    } catch (error) {
      console.error("Error fetching API key token:", error);
      showAlert("destructive", "Failed to fetch API key token. Please try again.");
    } finally {
      setIsLoadingToken(false);
    }
  };

  const handleCloseViewDialog = () => {
    setIsViewDialogOpen(false);
    setSelectedApiKey(null);
    setSelectedToken(null);
  };

  return (
    <div className="space-y-6">
      {/* Alert */}
      {alert && (
        <Alert
          variant={alert.type}
          title={alert.type === "success" ? "Success" : "Error"}
          description={alert.message}
          showCloseIcon
          onClose={() => setAlert(null)}
        />
      )}

      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-semibold text-gray-900">API Keys</h2>
          <p className="text-sm text-gray-600 mt-1">
            Manage your API keys for accessing the platform programmatically.
          </p>
        </div>
        <Button
          onClick={() => setIsCreateDialogOpen(true)}
          className="flex items-center gap-2"
          variant={"primary"}
        >
          <Plus className="h-4 w-4" />
          Create New API Key
        </Button>
      </div>

      {/* Table */}
      <ApiKeysTable
        apiKeys={apiKeys}
        onRowClick={handleRowClick}
        isLoading={isLoading}
      />

      {/* Create Dialog */}
      <CreateApiKeyDialog
        isOpen={isCreateDialogOpen}
        onClose={() => setIsCreateDialogOpen(false)}
        onSubmit={handleCreateApiKey}
        isLoading={isCreating}
      />

      {/* View Dialog */}
      <ViewApiKeyDialog
        isOpen={isViewDialogOpen}
        onClose={handleCloseViewDialog}
        apiKey={selectedApiKey}
        token={selectedToken}
        isLoading={isLoadingToken}
      />
    </div>
  );
};

export default ApiKeys;
