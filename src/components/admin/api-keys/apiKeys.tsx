import React, { useState, useEffect, useRef, useCallback } from "react";
import { Button } from "components/shadcn/ui/button";
import { Plus } from "lucide-react";
import ApiKeysTable from "./apiKeysTable";
import CreateApiKeyDialog from "./createApiKeyDialog";
import ViewApiKeyDialog from "./viewApiKeyDialog";
import { ApiKey, CreateApiKeyPayload } from "../types";
import { getApiKeys, createApiKey, getApiKeyToken } from "../services";

const ApiKeys: React.FC = () => {
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [isLoadingToken, setIsLoadingToken] = useState(false);
  const [selectedApiKey, setSelectedApiKey] = useState<ApiKey | null>(null);
  const [selectedToken, setSelectedToken] = useState<string | null>(null);

  const hasFetchedRef = useRef(false);



  const fetchApiKeys = useCallback(async () => {
    try {
      setIsLoading(true);
      const keys = await getApiKeys();
      setApiKeys(keys);
    } catch (error) {
      console.error("Error fetching API keys:", error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (!hasFetchedRef.current) {
      hasFetchedRef.current = true;
      fetchApiKeys();
    }
  }, [fetchApiKeys]);

  const handleCreateApiKey = async (payload: CreateApiKeyPayload) => {
    try {
      setIsCreating(true);
      const result = await createApiKey(payload);
      return result;
    } catch (error) {
      console.error("Error creating API key:", error);
      throw error;
    } finally {
      setIsCreating(false);
    }
  };

  const handleCreateSuccess = async () => {
    await fetchApiKeys(); // Refresh the list when API key is created successfully
  };

  const handleRowClick = async (apiKey: ApiKey) => {
    try {
      setSelectedApiKey(apiKey);
      setSelectedToken(null);
      setIsViewDialogOpen(true);
      setIsLoadingToken(true);

      const tokenResponse = await getApiKeyToken(apiKey.id);
      setSelectedToken(tokenResponse.token);
    } catch (error) {
      console.error("Error fetching API key token:", error);
    } finally {
      setIsLoadingToken(false);
    }
  };

  const handleCloseViewDialog = () => {
    setIsViewDialogOpen(false);
    setSelectedApiKey(null);
    setSelectedToken(null);
  };

  return (
    <div>
      <div className="flex justify-between items-center flex-wrap gap-4 p-8">
        <div>
          <h2 className="text-2xl font-semibold text-gray-900 text-left pl-0">API Keys</h2>
          <p className="text-sm text-gray-600 mt-1">
            Manage your API keys for accessing the platform programmatically.
          </p>
        </div>
        <Button
          onClick={() => setIsCreateDialogOpen(true)}
          className="flex items-center gap-2"
          variant={"primary"}
        >
          <Plus className="h-4 w-4" />
          Create New API Key
        </Button>
      </div>

      <div className="p-10">
        <ApiKeysTable
          apiKeys={apiKeys}
          onViewClick={handleRowClick}
          isLoading={isLoading}
        />
      </div>

      <CreateApiKeyDialog
        isOpen={isCreateDialogOpen}
        onClose={() => setIsCreateDialogOpen(false)}
        onSubmit={handleCreateApiKey}
        onSuccess={handleCreateSuccess}
        isLoading={isCreating}
      />

      <ViewApiKeyDialog
        isOpen={isViewDialogOpen}
        onClose={handleCloseViewDialog}
        apiKey={selectedApiKey}
        token={selectedToken}
        isLoading={isLoadingToken}
      />
    </div>
  );
};

export default ApiKeys;
