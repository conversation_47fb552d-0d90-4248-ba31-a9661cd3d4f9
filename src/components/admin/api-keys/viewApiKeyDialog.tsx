import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
} from "components/shadcn/ui/dialog";
import { Button } from "components/shadcn/ui/button";
import { Badge } from "components/shadcn/ui/badge";
import { Eye, EyeOff, Copy, Check } from "lucide-react";
import { ApiKey } from "../types";

interface ViewApiKeyDialogProps {
  isOpen: boolean;
  onClose: () => void;
  apiKey: ApiKey | null;
  token: string | null;
  isLoading?: boolean;
}

const ViewApiKeyDialog: React.FC<ViewApiKeyDialogProps> = ({
  isOpen,
  onClose,
  apiKey,
  token,
  isLoading = false,
}) => {
  const [isTokenVisible, setIsTokenVisible] = useState(false);
  const [isCopied, setIsCopied] = useState(false);

  // Reset state when dialog opens/closes
  useEffect(() => {
    if (!isOpen) {
      setIsTokenVisible(false);
      setIsCopied(false);
    }
  }, [isOpen]);

  const maskToken = (token: string) => {
    if (!token) return "";
    const visibleChars = 8;
    const maskedPart = "*".repeat(Math.max(0, token.length - visibleChars));
    const visiblePart = token.slice(-visibleChars);
    return maskedPart + visiblePart;
  };

  const handleCopyToken = async () => {
    if (!token) return;
    
    try {
      await navigator.clipboard.writeText(token);
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
    } catch (error) {
      console.error("Failed to copy token:", error);
    }
  };

  const toggleTokenVisibility = () => {
    setIsTokenVisible(!isTokenVisible);
  };

  if (!apiKey) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>API Key Details</DialogTitle>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <label className="text-sm font-medium text-gray-700">Name</label>
            <div className="text-sm text-gray-900 font-medium">{apiKey.name}</div>
          </div>
          
          <div className="grid gap-2">
            <label className="text-sm font-medium text-gray-700">Scopes</label>
            <div className="flex flex-wrap gap-1">
              {apiKey.scopes.map((scope, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {scope}
                </Badge>
              ))}
            </div>
          </div>
          
          <div className="grid gap-2">
            <label className="text-sm font-medium text-gray-700">API Key Token</label>
            {isLoading ? (
              <div className="flex items-center justify-center py-4">
                <div className="text-sm text-gray-500">Loading token...</div>
              </div>
            ) : token ? (
              <div className="space-y-2">
                <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-md border">
                  <code className="flex-1 text-sm font-mono break-all">
                    {isTokenVisible ? token : maskToken(token)}
                  </code>
                  <div className="flex gap-1">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={toggleTokenVisibility}
                      className="h-8 w-8"
                      title={isTokenVisible ? "Hide token" : "Show token"}
                    >
                      {isTokenVisible ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={handleCopyToken}
                      className="h-8 w-8"
                      title="Copy token"
                    >
                      {isCopied ? (
                        <Check className="h-4 w-4 text-green-600" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>
                <div className="text-xs text-gray-500">
                  Keep this token secure. It won't be shown again.
                </div>
              </div>
            ) : (
              <div className="text-sm text-red-500">Failed to load token</div>
            )}
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ViewApiKeyDialog;
