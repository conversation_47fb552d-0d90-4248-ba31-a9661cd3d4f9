import React from "react";
import { type ColumnDef, getCoreRowModel, useReactTable, flexRender } from "@tanstack/react-table";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "components/shadcn/ui/table";
import { Badge } from "components/shadcn/ui/badge";
import { ApiKey } from "../types";

interface ApiKeysTableProps {
  apiKeys: ApiKey[];
  onRowClick: (apiKey: ApiKey) => void;
  isLoading?: boolean;
}

const ApiKeysTable: React.FC<ApiKeysTableProps> = ({ apiKeys, onRowClick, isLoading }) => {
  const columns: ColumnDef<ApiKey>[] = [
    {
      accessorKey: "name",
      header: "Name",
      cell: ({ row }) => (
        <div className="font-medium text-gray-900 cursor-pointer hover:text-primary-600">
          {row.getValue("name")}
        </div>
      ),
    },
    {
      accessorKey: "scopes",
      header: "Scopes",
      cell: ({ row }) => {
        const scopes = row.getValue("scopes") as string[];
        return (
          <div className="flex flex-wrap gap-1">
            {scopes.map((scope, index) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {scope}
              </Badge>
            ))}
          </div>
        );
      },
    },
  ];

  const table = useReactTable({
    data: apiKeys,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="text-gray-500">Loading API keys...</div>
      </div>
    );
  }

  if (apiKeys.length === 0) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="text-gray-500">No API keys found. Create your first API key to get started.</div>
      </div>
    );
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <TableHead key={header.id} className="font-semibold">
                  {header.isPlaceholder
                    ? null
                    : flexRender(header.column.columnDef.header, header.getContext())}
                </TableHead>
              ))}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows.map((row) => (
            <TableRow
              key={row.id}
              className="cursor-pointer hover:bg-gray-50"
              onClick={() => onRowClick(row.original)}
            >
              {row.getVisibleCells().map((cell) => (
                <TableCell key={cell.id}>
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default ApiKeysTable;
