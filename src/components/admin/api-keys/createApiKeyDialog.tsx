import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "components/shadcn/ui/dialog";
import { Button } from "components/shadcn/ui/button";
import { Input } from "components/shadcn/ui/input";
import { Label } from "components/shadcn/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "components/shadcn/ui/select";
import { Checkbox } from "components/shadcn/ui/checkbox";
import { Scope, CreateApiKeyPayload } from "../types";

interface CreateApiKeyDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (payload: CreateApiKeyPayload) => Promise<void>;
  isLoading?: boolean;
}

const CreateApiKeyDialog: React.FC<CreateApiKeyDialogProps> = ({
  isOpen,
  onClose,
  onSubmit,
  isLoading = false,
}) => {
  const [name, setName] = useState("");
  const [selectedScopes, setSelectedScopes] = useState<string[]>([]);
  const [errors, setErrors] = useState<{ name?: string; scopes?: string }>({});

  const scopeOptions = [
    { value: Scope.DealCreate, label: "Deal Create" },
    { value: Scope.CampaignRead, label: "Campaign Read" },
    { value: Scope.UserRead, label: "User Read" },
  ];

  const handleScopeChange = (scope: string, checked: boolean) => {
    if (checked) {
      setSelectedScopes(prev => [...prev, scope]);
    } else {
      setSelectedScopes(prev => prev.filter(s => s !== scope));
    }
    // Clear scope error when user makes a selection
    if (errors.scopes) {
      setErrors(prev => ({ ...prev, scopes: undefined }));
    }
  };

  const validateForm = () => {
    const newErrors: { name?: string; scopes?: string } = {};
    
    if (!name.trim()) {
      newErrors.name = "Name is required";
    }
    
    if (selectedScopes.length === 0) {
      newErrors.scopes = "At least one scope must be selected";
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit({
        name: name.trim(),
        scopes: selectedScopes,
      });
      handleClose();
    } catch (error) {
      console.error("Error creating API key:", error);
    }
  };

  const handleClose = () => {
    setName("");
    setSelectedScopes([]);
    setErrors({});
    onClose();
  };

  const handleNameChange = (value: string) => {
    setName(value);
    // Clear name error when user starts typing
    if (errors.name) {
      setErrors(prev => ({ ...prev, name: undefined }));
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create New API Key</DialogTitle>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => handleNameChange(e.target.value)}
              placeholder="Enter API key name"
              className={errors.name ? "border-red-500" : ""}
            />
            {errors.name && (
              <span className="text-sm text-red-500">{errors.name}</span>
            )}
          </div>
          
          <div className="grid gap-2">
            <Label>Scopes</Label>
            <div className="space-y-2">
              {scopeOptions.map((option) => (
                <div key={option.value} className="flex items-center space-x-2">
                  <Checkbox
                    id={option.value}
                    checked={selectedScopes.includes(option.value)}
                    onCheckedChange={(checked) => 
                      handleScopeChange(option.value, checked as boolean)
                    }
                  />
                  <Label htmlFor={option.value} className="text-sm font-normal">
                    {option.label}
                  </Label>
                </div>
              ))}
            </div>
            {errors.scopes && (
              <span className="text-sm text-red-500">{errors.scopes}</span>
            )}
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isLoading}>
            {isLoading ? "Creating..." : "Create API Key"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CreateApiKeyDialog;
