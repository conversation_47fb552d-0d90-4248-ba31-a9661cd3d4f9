import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "components/shadcn/ui/dialog";
import { Button } from "components/shadcn/ui/button";
import { Input } from "components/shadcn/ui/input";
import { Label } from "components/shadcn/ui/label";
import { Badge } from "components/shadcn/ui/badge";
import { Checkbox } from "components/shadcn/ui/checkbox";
import { Eye, EyeOff, Copy, Check, X } from "lucide-react";
import { Scope, CreateApiKeyPayload, CreateApiKeyResponse } from "../types";

interface CreateApiKeyDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (payload: CreateApiKeyPayload) => Promise<CreateApiKeyResponse>;
  isLoading?: boolean;
}

const CreateApiKeyDialog: React.FC<CreateApiKeyD<PERSON>ogProps> = ({
  isO<PERSON>,
  onClose,
  onSubmit,
  isLoading = false,
}) => {
  const [name, setName] = useState("");
  const [selectedScopes, setSelectedScopes] = useState<string[]>([]);
  const [errors, setErrors] = useState<{ name?: string; scopes?: string }>({});
  const [createdApiKey, setCreatedApiKey] = useState<CreateApiKeyResponse | null>(null);
  const [generatedToken, setGeneratedToken] = useState<string | null>(null);
  const [isTokenVisible, setIsTokenVisible] = useState(false);
  const [isCopied, setIsCopied] = useState(false);

  const scopeOptions = [
    { value: Scope.DealCreate, label: Scope.DealCreate },
    { value: Scope.CampaignRead, label: Scope.CampaignRead },
    { value: Scope.UserRead, label: Scope.UserRead },
  ];

  const handleScopeChange = (scope: string, checked: boolean) => {
    if (checked) {
      setSelectedScopes(prev => [...prev, scope]);
    } else {
      setSelectedScopes(prev => prev.filter(s => s !== scope));
    }
    if (errors.scopes) {
      setErrors(prev => ({ ...prev, scopes: undefined }));
    }
  };

  const validateForm = () => {
    const newErrors: { name?: string; scopes?: string } = {};

    if (!name.trim()) {
      newErrors.name = "Name is required";
    }

    if (selectedScopes.length === 0) {
      newErrors.scopes = "At least one scope must be selected";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      const result = await onSubmit({
        name: name.trim(),
        scopes: selectedScopes,
      });
      setCreatedApiKey(result);
      setGeneratedToken(result.token);
    } catch (error) {
      console.error("Error creating API key:", error);
    }
  };

  const handleClose = () => {
    setName("");
    setSelectedScopes([]);
    setErrors({});
    setCreatedApiKey(null);
    setGeneratedToken(null);
    setIsTokenVisible(false);
    setIsCopied(false);
    onClose();
  };

  const maskToken = (token: string) => {
    return "•".repeat(token.length);
  };

  const handleCopyToken = async () => {
    if (!generatedToken) return;

    try {
      await navigator.clipboard.writeText(generatedToken);
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
    } catch (error) {
      console.error("Failed to copy token:", error);
    }
  };

  const toggleTokenVisibility = () => {
    setIsTokenVisible(!isTokenVisible);
  };

  const handleNameChange = (value: string) => {
    setName(value);
    if (errors.name) {
      setErrors(prev => ({ ...prev, name: undefined }));
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {generatedToken ? `Generated API Key - ${createdApiKey?.name}` : "Create New API Key"}
          </DialogTitle>
          {generatedToken && (
            <button
              onClick={handleClose}
              className="absolute top-2 right-2 transition-all"
              aria-label="Close"
            >
              <X className="h-5 w-5" />
            </button>
          )}
        </DialogHeader>

        {!generatedToken ? (
          <>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  value={name}
                  onChange={(e) => handleNameChange(e.target.value)}
                  placeholder="Enter API key name"
                  className={errors.name ? "border-red-500" : ""}
                />
                {errors.name && (
                  <span className="text-sm text-red-500">{errors.name}</span>
                )}
              </div>

              <div className="grid gap-2">
                <Label>Scopes</Label>
                <div className="space-y-2">
                  {scopeOptions.map((option) => (
                    <div key={option.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={option.value}
                        checked={selectedScopes.includes(option.value)}
                        onCheckedChange={(checked) =>
                          handleScopeChange(option.value, checked as boolean)
                        }
                      />
                      <Label htmlFor={option.value} className="text-sm font-normal">
                        {option.label}
                      </Label>
                    </div>
                  ))}
                </div>
                {errors.scopes && (
                  <span className="text-sm text-red-500">{errors.scopes}</span>
                )}
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={handleClose} disabled={isLoading}>
                Cancel
              </Button>
              <Button variant="primary" onClick={handleSubmit} disabled={isLoading}>
                {isLoading ? "Creating..." : "Create API Key"}
              </Button>
            </DialogFooter>
          </>
        ) : (
          <>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label>Scopes</Label>
                <div className="flex flex-wrap gap-1">
                  {createdApiKey?.scopes.map((scope, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {scope}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="grid gap-2">
                <Label>Generated API Key Token</Label>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-md border">
                    <code className="flex-1 text-sm font-mono break-all">
                      {isTokenVisible ? generatedToken : maskToken(generatedToken!)}
                    </code>
                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={toggleTokenVisibility}
                        className="h-8 w-8"
                        title={isTokenVisible ? "Hide token" : "Show token"}
                      >
                        {isTokenVisible ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={handleCopyToken}
                        className="h-8 w-8"
                        title="Copy token"
                      >
                        {isCopied ? (
                          <Check className="h-4 w-4 text-green-600" />
                        ) : (
                          <Copy className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                  <div className="text-xs text-gray-500">
                    Keep this token secure.
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default CreateApiKeyDialog;
