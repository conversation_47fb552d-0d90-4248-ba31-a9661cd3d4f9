import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "components/shadcn/ui/dialog";
import { Button } from "components/shadcn/ui/button";
import { Input } from "components/shadcn/ui/input";
import { Label } from "components/shadcn/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "components/shadcn/ui/select";
import { Scope, CreateApiKeyPayload } from "../types";

interface CreateApiKeyDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (payload: CreateApiKeyPayload) => Promise<void>;
  isLoading?: boolean;
}

const CreateApiKeyDialog: React.FC<CreateApiKeyDialogProps> = ({
  isOpen,
  onClose,
  onSubmit,
  isLoading = false,
}) => {
  const [name, setName] = useState("");
  const [selectedScope, setSelectedScope] = useState<string>("");
  const [errors, setErrors] = useState<{ name?: string; scope?: string }>({});

  const scopeOptions = [
    { value: Scope.DealCreate, label: "Deal Create" },
    { value: Scope.CampaignRead, label: "Campaign Read" },
    { value: Scope.UserRead, label: "User Read" },
  ];

  const handleScopeChange = (scope: string) => {
    setSelectedScope(scope);
    // Clear scope error when user makes a selection
    if (errors.scope) {
      setErrors(prev => ({ ...prev, scope: undefined }));
    }
  };

  const validateForm = () => {
    const newErrors: { name?: string; scope?: string } = {};

    if (!name.trim()) {
      newErrors.name = "Name is required";
    }

    if (!selectedScope) {
      newErrors.scope = "A scope must be selected";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit({
        name: name.trim(),
        scopes: [selectedScope],
      });
      handleClose();
    } catch (error) {
      console.error("Error creating API key:", error);
    }
  };

  const handleClose = () => {
    setName("");
    setSelectedScope("");
    setErrors({});
    onClose();
  };

  const handleNameChange = (value: string) => {
    setName(value);
    // Clear name error when user starts typing
    if (errors.name) {
      setErrors(prev => ({ ...prev, name: undefined }));
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create New API Key</DialogTitle>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => handleNameChange(e.target.value)}
              placeholder="Enter API key name"
              className={errors.name ? "border-red-500" : ""}
            />
            {errors.name && (
              <span className="text-sm text-red-500">{errors.name}</span>
            )}
          </div>

          <div className="grid gap-2">
            <Label htmlFor="scope">Scope</Label>
            <Select value={selectedScope} onValueChange={handleScopeChange}>
              <SelectTrigger className={errors.scope ? "border-red-500" : ""}>
                <SelectValue placeholder="Select a scope" />
              </SelectTrigger>
              <SelectContent>
                {scopeOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.scope && (
              <span className="text-sm text-red-500">{errors.scope}</span>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isLoading}>
            {isLoading ? "Creating..." : "Create API Key"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CreateApiKeyDialog;
