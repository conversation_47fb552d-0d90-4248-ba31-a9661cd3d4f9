export type AddDkimPayload = {
  name: string;
  value: string;
};

type ProductPricing = {
  id: string;
  price: number;
  currency: string;
  interval: string;
};

export interface UserSearchResponse {
  uid: string;
  name: string;
  email: string;
  stripeCustomerID: string;
  isBiz4Biz: boolean;
  isHarbourClub: boolean;
  createdAt: string;
  updatedAt: string;
  quotaUpdatedAt: string;
  subscription: {
    id: string;
    userId: string;
    status: string;
    daysUntilDue: number;
    startDate: string;
    endedAt: string;
    trialStart: string;
    trialEnd: string;
    cancelAt: string;
    cancelledAt: string;
    productName: string;
    productDisplayName: string;
    productPricing: ProductPricing;
    userFeatureQuotas: {
      id: string;
      featureName: string;
      quota: number;
      remainingQuota: number;
      totalQuota: number;
    }[];
  };
}

export interface UpdateQuotaPayload {
  quotas: {
    userFeatureQuotaId: string;
    quota: number;
  }[];
}

export type QuotaCategory = "Letters" | "Collections" | "Email";
export type QuotaData = Record<QuotaCategory, number>;

// API Keys related types
export enum Scope {
  DealCreate = "deals:create",
  CampaignRead = "campaigns:read",
  UserRead = "users:read",
}

export interface ApiKey {
  id: string;
  name: string;
  scopes: string[];
}

export interface CreateApiKeyPayload {
  name: string;
  scopes: string[];
}

export interface ApiKeyTokenResponse {
  token: string;
}

export interface CreateApiKeyResponse {
  id: string;
  name: string;
  scopes: string[];
  token: string;
}
